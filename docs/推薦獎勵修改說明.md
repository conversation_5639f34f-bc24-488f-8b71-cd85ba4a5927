# 推薦獎勵分配機制修改說明

## 修改概述

根據需求，將推薦獎勵的分配機制從「直屬推薦人單獨領取」修改為「全體合夥人依權值分配」。

## 原有機制

**廣告會員升級購買中脈課程時：**
- 由直屬推薦人依個人上課程度燒傷單獨領取20%推薦獎勵

## 修改後機制

**廣告會員升級購買中脈課程時：**
- 由全體合夥人依個人上課程度燒傷及合夥人級別權值領取

## 具體修改內容

### 1. OrderHelper.php 修改

**位置：** `app/Services/pattern/OrderHelper.php` 第2063-2073行

**原代碼：**
```php
/*計算 購買者的推薦者 的「應分潤金額」(推廣獎勵)*/
$share_cv_vip_type_burn = $BonusHelper->count_share_cv_vip_type($product, $upline_user);
$available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 1, $share_cv_vip_type_burn);
$share_cv_allocated += $available_cv;
$BonusHelper->add_available_cv($upline_user, $available_cv);
```

**修改後：**
```php
/*計算 推廣獎勵 的「應分潤金額」*/
/*修改為：由全體合夥人依個人上課程度燒傷及合夥人級別權值領取*/
$recommend_total_cv = $BonusHelper->distribute_recommend_bonus_to_partners($product, $user_id, $share_cv);
$share_cv_allocated += $recommend_total_cv;
```

### 2. BonusHelper.php 新增方法

**位置：** `app/Services/pattern/BonusHelper.php` 第1395-1430行

**新增方法：**
```php
/**
 * 依商品與購買者id，分配推薦獎勵給全體合夥人(依個人上課程度燒傷及合夥人級別權值)
 */
public function distribute_recommend_bonus_to_partners($product, int $buyer_id, float $share_cv)
{
    /*取得推廣獎勵的總分潤金額*/
    $recommend_total_cv = $this->count_available_cv($product['bonus_model_id'], 1, $share_cv);
    
    /*排除購買者後取得有效合夥人加權結果*/
    $weight_result = $this->get_active_partner_weight_result($buyer_id);
    
    /*計算各有效合夥人可分金額(依個人上課程度燒傷及合夥人級別權值)*/
    $total_weight = $weight_result['total_weight'];
    $active_partner = $weight_result['user_weight'];
    
    if ($total_weight > 0) {
        foreach ($active_partner as $partner_id => $weight) {
            /*計算該合夥人應分配的推廣獎勵比例*/
            $partner_ratio = $weight / $total_weight;
            $partner_recommend_cv = $recommend_total_cv * $partner_ratio;
            
            /*依該合夥人的vip等級進行燒傷計算*/
            $share_cv_vip_type_burn = $this->count_share_cv_vip_type($product, $partner_id);
            /*取得該合夥人在燒傷限制下的推廣獎勵比例*/
            $full_recommend_cv = $this->count_available_cv($product['bonus_model_id'], 1, $share_cv);
            $burned_recommend_cv = $this->count_available_cv($product['bonus_model_id'], 1, $share_cv_vip_type_burn);
            $burn_ratio = $full_recommend_cv > 0 ? $burned_recommend_cv / $full_recommend_cv : 0;
            
            /*最終分配金額 = 權值分配 * 燒傷限制*/
            $final_available_cv = $partner_recommend_cv * $burn_ratio;
            $this->add_available_cv($partner_id, $final_available_cv);
        }
    }
    
    return $recommend_total_cv;
}
```

## 分配邏輯說明

### 範例：中脈優惠價 6600 CV3740

假設有以下合夥人：
- 任督級別合夥人：權重 10
- 中脈級別合夥人：權重 20  
- 法身級別合夥人：權重 30

**分配步驟：**

1. **計算推廣獎勵總額**
   - 分享CV：6600 × 30% = 1980
   - 推廣獎勵：1980 × 20% = 396

2. **計算權重分配**
   - 總權重：10 + 20 + 30 = 60
   - 任督分配比例：10/60 = 16.67%
   - 中脈分配比例：20/60 = 33.33%
   - 法身分配比例：30/60 = 50%

3. **應用燒傷機制**
   - 任督：396 × 16.67% × (1320/3740) = 23.3
   - 中脈：396 × 33.33% × (3740/3740) = 132
   - 法身：396 × 50% × (3740/3740) = 198

4. **最終分配結果**
   - 任督級別：23.3 CV
   - 中脈級別：132 CV
   - 法身級別：198 CV
   - 總計：353.3 CV（剩餘42.7 CV由系統回收）

## 燒傷機制說明

燒傷機制確保合夥人只能根據自己的課程進度等級來獲得相應的獎勵：

- **任督級別**：最多只能分配1320 CV的獎勵
- **中脈級別**：最多只能分配3740 CV的獎勵  
- **法身級別**：最多只能分配11000 CV的獎勵

當商品CV超過合夥人等級限制時，會按比例縮減獎勵。

## 影響範圍

此修改主要影響：
1. 廣告會員購買課程商品時的推薦獎勵分配
2. 所有有效合夥人都有機會獲得推薦獎勵
3. 獎勵分配更加公平，基於合夥人級別權重
4. 保持燒傷機制，確保獎勵與個人課程進度相符
